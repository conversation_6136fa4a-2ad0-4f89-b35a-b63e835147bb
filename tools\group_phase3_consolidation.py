import json
import os
import hashlib

INPUT_FILE = r"C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\documents_filelist_tagged.jsonl"
OUTPUT_FILE = r"C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\phase3_reconstruction_map.jsonl"

# Load relevant tags to include
ACCEPTED_TAGS = {"code_candidate", "config_file", "backend_logic", "ui_component"}

def hash_file(path):
    try:
        with open(path, 'rb') as f:
            return hashlib.sha256(f.read()).hexdigest()
    except:
        return None

def main():
    if not os.path.exists(INPUT_FILE):
        print(f"[ERROR] File not found: {INPUT_FILE}")
        return

    seen_hashes = {}
    groups = []

    with open(INPUT_FILE, "r", encoding="utf-8") as infile:
        for line in infile:
            try:
                entry = json.loads(line)
                if entry.get("notes") not in ACCEPTED_TAGS:
                    continue

                file_path = entry.get("full_path")
                file_hash = hash_file(file_path)
                entry["hash"] = file_hash if file_hash else "unreadable"

                if file_hash:
                    if file_hash not in seen_hashes:
                        seen_hashes[file_hash] = []
                    seen_hashes[file_hash].append(entry)

            except Exception as e:
                print(f"[WARN] Could not parse or hash: {e}")

    for hash_val, entries in seen_hashes.items():
        if len(entries) > 1:
            group = {
                "hash": hash_val,
                "count": len(entries),
                "files": entries
            }
            groups.append(group)

    with open(OUTPUT_FILE, "w", encoding="utf-8") as outfile:
        for group in groups:
            outfile.write(json.dumps(group) + "\n")

    print(f"[✓] Duplicate & grouping map saved to: {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
