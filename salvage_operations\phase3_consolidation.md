# Phase 3: Consolidation & Reconstruction Protocol

## What to Analyze

- Use this file as input:
  salvage_operations\inventory\documents_filelist_tagged.jsonl

- For each entry:
  - Group files by filename and extension
  - Hash file contents to detect exact duplicates
  - Identify modules that appear across multiple project fragments

## What to Output

- Save grouped and hash-mapped results to:
  salvage_operations\inventory\phase3_reconstruction_map.jsonl

## Required Behavior

- CLI Mode only
- Do not simulate groupings
- Read real file paths from full_path field
- Include tags from previous phase (notes) in groupings

## Tags of Interest

Focus on entries with these notes:
- code_candidate
- config_file
- backend_logic
- ui_component

## Goals

- Identify reusable components
- Prepare for module reconstruction
- Tag probable duplicate files and related paths
