import json
import os
import hashlib

INPUT_FILE = r"C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\documents_filelist_tagged.jsonl"
OUTPUT_FILE = r"C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\phase3_reconstruction_map.jsonl"

ACCEPTED_TAGS = {"code_candidate", "config_file", "backend_logic", "ui_component"}

def hash_file(path):
    try:
        with open(path, 'rb') as f:
            return hashlib.sha256(f.read()).hexdigest()
    except Exception as e:
        print(f"[SKIP] Could not read: {path} — {str(e)}")
        return None

def main():
    if not os.path.exists(INPUT_FILE):
        print(f"[ERROR] Input file not found: {INPUT_FILE}")
        return

    seen_hashes = {}
    total_processed = 0
    total_skipped = 0

    print(f"[START] Reading tagged file list from: {INPUT_FILE}")

    with open(INPUT_FILE, "r", encoding="utf-8") as infile:
        for line_num, line in enumerate(infile, 1):
            try:
                entry = json.loads(line)
                if entry.get("notes") not in ACCEPTED_TAGS:
                    continue

                file_path = entry.get("full_path")
                file_hash = hash_file(file_path)
                if not file_hash:
                    total_skipped += 1
                    continue

                entry["hash"] = file_hash
                if file_hash not in seen_hashes:
                    seen_hashes[file_hash] = []
                seen_hashes[file_hash].append(entry)

                total_processed += 1
                if total_processed % 1000 == 0:
                    print(f"[HASH] Processed {total_processed} files...")

            except Exception as e:
                print(f"[WARN] Failed to parse line {line_num}: {str(e)}")
                total_skipped += 1

    print(f"[SAVE] Writing grouped hash map to: {OUTPUT_FILE}")

    with open(OUTPUT_FILE, "w", encoding="utf-8") as outfile:
        for hash_val, entries in seen_hashes.items():
            if len(entries) > 1:
                group = {
                    "hash": hash_val,
                    "count": len(entries),
                    "files": entries
                }
                outfile.write(json.dumps(group) + "\n")

    print(f"[✓] Done. Processed: {total_processed}, Skipped: {total_skipped}, Duplicates: {len(seen_hashes)}")

if __name__ == "__main__":
    main()
