---
type: "always_apply"
---

# Phase 2: Code Analysis Protocol (CLI Mode)

## What to Analyze

- Use this file as input:
  salvage_operations\inventory\documents_filelist.jsonl

- For each entry, evaluate the file type and context.

## What to Tag

- Add or update the "notes" field with one or more of the following categories:
  - code_candidate
  - duplicate_name
  - exact_duplicate
  - config_file
  - backend_logic
  - ui_component
  - unclear_purpose
  - legacy_fragment
  - test_file

## What to Output

- Save results to:
  salvage_operations\inventory\documents_filelist_tagged.jsonl

## Required Mode

- CLI Mode only
- Do not simulate results or draft summaries
- Run only real analysis using file paths and contents
- Do not delete or move files — only tag them for review
