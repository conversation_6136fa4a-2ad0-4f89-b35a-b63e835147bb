$OutputFile = 'C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\phase3_reconstruction_map.jsonl'

if (-Not (Test-Path $OutputFile)) {
    Write-Host "[!] Phase 3 output file does not exist yet."
    exit 1
}

$previousCount = 0
$previousSize = 0

while ($true) {
    if (Test-Path $OutputFile) {
        $lineCount = (Get-Content $OutputFile -ErrorAction SilentlyContinue | Measure-Object -Line).Lines
        $fileSize = (Get-Item $OutputFile).Length
        $modified = (Get-Item $OutputFile).LastWriteTime

        Write-Host "`n[$(Get-Date -Format 'HH:mm:ss')] Phase 3 Monitor"
        Write-Host "Lines written    : $lineCount"
        Write-Host "File size (bytes): $fileSize"
        Write-Host "Last modified    : $modified"

        if ($lineCount -eq $previousCount -and $fileSize -eq $previousSize) {
            Write-Host "[~] No change since last check."
        } else {
            Write-Host "[✓] Phase 3 writing in progress..."
        }

        $previousCount = $lineCount
        $previousSize = $fileSize
    }
    else {
        Write-Host "[!] File missing."
    }

    Start-Sleep -Seconds 120
}
