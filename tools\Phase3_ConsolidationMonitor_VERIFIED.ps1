# Phase 3 Reconstruction Monitor - Fixed Version
# Press Ctrl+C to exit

$OutputFile = 'C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\phase3_reconstruction_map.jsonl'

$previousCount = 0
$previousSize = 0

Write-Host "=== Phase 3 Reconstruction Monitor ===" -ForegroundColor Cyan
Write-Host "Monitoring: $OutputFile" -ForegroundColor Gray
Write-Host "Press Ctrl+C to exit" -ForegroundColor Yellow
Write-Host ""

# Function to count lines efficiently
function Get-LineCount {
    param($FilePath)
    try {
        if (-Not (Test-Path $FilePath)) {
            return 0
        }
        
        $lineCount = 0
        $reader = [System.IO.File]::OpenText($FilePath)
        try {
            while ($reader.ReadLine() -ne $null) {
                $lineCount++
            }
        }
        finally {
            $reader.Close()
        }
        return $lineCount
    }
    catch {
        return 0
    }
}

# Main monitoring loop
while ($true) {
    try {
        if (Test-Path $OutputFile) {
            $lineCount = Get-LineCount -FilePath $OutputFile
            $fileInfo = Get-Item $OutputFile -ErrorAction Stop
            $fileSize = $fileInfo.Length
            $modified = $fileInfo.LastWriteTime

            $timestamp = Get-Date -Format 'HH:mm:ss'
            Write-Host "`n[$timestamp] Phase 3 Monitor" -ForegroundColor Green
            Write-Host "Lines written    : $lineCount" -ForegroundColor White
            Write-Host "File size (bytes): $fileSize" -ForegroundColor White
            Write-Host "Last modified    : $modified" -ForegroundColor White

            if ($lineCount -eq $previousCount -and $fileSize -eq $previousSize) {
                Write-Host "[~] No change since last check." -ForegroundColor Yellow
            } else {
                Write-Host "[✓] Phase 3 writing in progress..." -ForegroundColor Green
            }

            $previousCount = $lineCount
            $previousSize = $fileSize
        }
        else {
            $timestamp = Get-Date -Format 'HH:mm:ss'
            Write-Host "`n[$timestamp] Phase 3 Monitor" -ForegroundColor Green
            Write-Host "[!] Waiting for file to be created..." -ForegroundColor Magenta
        }
    }
    catch {
        $timestamp = Get-Date -Format 'HH:mm:ss'
        Write-Host "`n[$timestamp] Phase 3 Monitor" -ForegroundColor Green
        Write-Host "[!] Error accessing file: $($_.Exception.Message)" -ForegroundColor Red
    }

    Start-Sleep -Seconds 120
}
